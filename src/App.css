/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
  position: relative;
}

.app {
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
}

/* Universal container to prevent overflow */
* {
  max-width: 100%;
}

/* Ensure all text elements can break properly */
h1, h2, h3, h4, h5, h6, p, span, div {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2rem;
}

/* Login Styles */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  width: 100%;
  overflow-x: hidden;
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
  min-width: 0;
}

.login-card h1 {
  text-align: center;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.login-card h2 {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
  font-weight: normal;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input {
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.login-button {
  padding: 0.75rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 1rem;
}

.login-button:hover:not(:disabled) {
  background: #5a6fd8;
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #e74c3c;
  text-align: center;
  padding: 0.5rem;
  background: #fdf2f2;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
}



/* Dashboard Styles */
.dashboard {
  min-height: 100vh;
  background: #f8f9fa;
  width: 100%;
  overflow-x: hidden;
}

.dashboard-header {
  background: white;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-width: 0;
  flex-wrap: nowrap;
  gap: 1rem;
}

.dashboard-header h1 {
  color: #333;
  font-size: 1.8rem;
  min-width: 0;
  flex-shrink: 0;
  white-space: nowrap;
}

.dashboard-title {
  cursor: pointer;
  transition: color 0.3s;
}

.dashboard-title:hover {
  color: #667eea;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
  min-width: 0;
}

.user-info span {
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.logout-button {
  padding: 0.5rem 1rem;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  flex-shrink: 0;
}

.logout-button:hover {
  background: #c82333;
}

/* Dashboard Navigation */
.dashboard-nav {
  background: white;
  border-bottom: 1px solid #dee2e6;
  padding: 0;
  display: flex;
  gap: 0;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.nav-button {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

.nav-button:hover {
  color: #333;
  background: #f8f9fa;
}

.nav-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9fa;
}

.dashboard-content {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  overflow-x: hidden;
}

.dashboard-section {
  background: white;
  margin-bottom: 2rem;
  padding: 1rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
  overflow-x: hidden;
}

.dashboard-section h2 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  width: 100%;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 10px;
  text-align: center;
}

.stat-card h3 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
}

/* Users Table */
.users-table-container {
  overflow-x: auto;
  width: 100%;
  -webkit-overflow-scrolling: touch;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  min-width: 600px;
}

.users-table th,
.users-table td {
  padding: 0.5rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
  white-space: nowrap;
}

.users-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.users-table tr:hover {
  background: #f8f9fa;
}

.role-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.role-badge.admin {
  background: #dc3545;
  color: white;
}

.role-badge.employee {
  background: #28a745;
  color: white;
}

/* Actions Grid */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  width: 100%;
}

.action-button {
  padding: 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s;
  min-width: 0;
  word-wrap: break-word;
}

.action-button:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

/* Employee Dashboard Specific */
.time-display {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.current-time h3 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.current-time p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Tasks */
.tasks-container {
  display: grid;
  gap: 1rem;
  width: 100%;
}

.task-card {
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid;
  background: #f8f9fa;
  width: 100%;
  overflow-wrap: break-word;
}

.task-card.pending {
  border-left-color: #ffc107;
}

.task-card.in-progress {
  border-left-color: #17a2b8;
}

.task-card.completed {
  border-left-color: #28a745;
}

.task-card h4 {
  margin-bottom: 0.5rem;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.task-card p {
  color: #666;
  margin-bottom: 0.5rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.task-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.task-card.pending .task-status {
  background: #ffc107;
  color: #212529;
}

.task-card.in-progress .task-status {
  background: #17a2b8;
  color: white;
}

.task-card.completed .task-status {
  background: #28a745;
  color: white;
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 5px;
}

.activity-time {
  font-weight: 500;
  color: #667eea;
  min-width: 80px;
}

.activity-text {
  color: #666;
}

/* Task Management Styles */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.task-header h2 {
  margin-bottom: 0;
}

.action-button.primary {
  background: #28a745;
}

.action-button.primary:hover {
  background: #218838;
}

.task-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.date-filter,
.status-filter,
.employee-filter {
  padding: 0.5rem;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

/* Force English locale for date inputs */
input[type="date"] {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
}

/* Ensure date inputs use English format */
input[type="date"]:lang(en-US) {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.date-filter:focus,
.status-filter:focus,
.employee-filter:focus {
  outline: none;
  border-color: #667eea;
}

/* Admin Task Cards */
.admin-tasks-container {
  display: grid;
  gap: 1.5rem;
}

.admin-task-card {
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 5px solid;
  background: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-task-card.pending {
  border-left-color: #ffc107;
}

.admin-task-card.in-progress {
  border-left-color: #17a2b8;
}

.admin-task-card.completed {
  border-left-color: #28a745;
}

.task-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.task-header-info h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.task-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.customer-info,
.service-info,
.assignment-info {
  padding: 1rem;
  background: white;
  border-radius: 5px;
  font-size: 0.9rem;
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn-edit,
.btn-delete,
.btn-assign,
.btn-view,
.btn-invoice,
.btn-view-employee {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.btn-edit {
  background: #667eea;
  color: white;
}

.btn-edit:hover {
  background: #5a6fd8;
}

.btn-delete {
  background: #dc3545;
  color: white;
}

.btn-delete:hover {
  background: #c82333;
}

.btn-assign,
.btn-view,
.btn-invoice,
.btn-view-employee {
  background: #6c757d;
  color: white;
}

.btn-assign:hover,
.btn-view:hover,
.btn-invoice:hover,
.btn-view-employee:hover {
  background: #5a6268;
}

/* Employee Overview */
.employee-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.employee-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.employee-card h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
}

.employee-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.employee-stats .stat {
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 5px;
  font-size: 0.9rem;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 0.5rem;
  }

  .login-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .login-card h1 {
    font-size: 1.3rem;
  }

  .dashboard-header {
    padding: 0.75rem 0.5rem;
    gap: 0.5rem;
  }

  .dashboard-header h1 {
    font-size: 1.3rem;
  }

  .user-info {
    gap: 0.5rem;
  }

  .user-info span {
    font-size: 0.85rem;
  }

  .dashboard-nav {
    padding: 0;
  }

  .nav-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .dashboard-content {
    padding: 0.5rem;
  }

  .dashboard-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .dashboard-section h2 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .stats-grid,
  .actions-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .current-time h3 {
    font-size: 2rem;
  }

  .current-time p {
    font-size: 1rem;
  }

  .time-display {
    padding: 1.5rem;
  }

  .users-table th,
  .users-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.9rem;
  }

  .task-card {
    padding: 0.75rem;
  }

  .task-card h4 {
    font-size: 1rem;
  }

  .task-card p {
    font-size: 0.9rem;
  }

  .activity-item {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .activity-time {
    min-width: 60px;
    font-size: 0.8rem;
  }

  .activity-text {
    font-size: 0.9rem;
  }

  .action-button {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  /* Task Management Mobile */
  .task-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .task-filters {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .admin-task-card {
    padding: 1rem;
  }

  .task-details {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .customer-info,
  .service-info,
  .assignment-info {
    padding: 0.75rem;
  }

  .task-actions {
    gap: 0.5rem;
  }

  .btn-edit,
  .btn-delete,
  .btn-assign,
  .btn-view,
  .btn-invoice,
  .btn-view-employee {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .employee-overview {
    grid-template-columns: 1fr;
  }

  .employee-card {
    padding: 1rem;
  }
}

/* Task Management Specific Styles */
.task-management {
  width: 100%;
}

.no-tasks {
  text-align: center;
  padding: 2rem;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 1rem 0;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.task-distance {
  font-size: 0.9rem;
  color: #666;
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

/* Employee Tasks Styles */
.employee-tasks {
  width: 100%;
}

.task-nav {
  display: flex;
  gap: 0;
  margin-bottom: 1rem;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.task-nav .nav-button {
  flex: 1;
  padding: 1rem;
  background: white;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  transition: all 0.3s;
  border-bottom: 3px solid transparent;
}

.task-nav .nav-button:hover {
  background: #f8f9fa;
  color: #333;
}

.task-nav .nav-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9fa;
}

.tasks-section h3 {
  margin-bottom: 1rem;
  color: #333;
}

/* Photo Upload Styles */
.photo-upload-section {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.photo-upload-section h5 {
  margin-bottom: 1rem;
  color: #333;
}

.photo-upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.photo-upload-area {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.photo-upload-area label {
  font-weight: 500;
  color: #555;
  font-size: 0.9rem;
}

.upload-zone {
  padding: 1rem;
  background: white;
  border: 2px dashed #ccc;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-zone:hover {
  border-color: #667eea;
  background: #f8f9fa;
}

.upload-zone p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.upload-zone input[type="file"] {
  margin-top: 0.5rem;
  font-size: 0.8rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
}

.task-detail-modal {
  max-width: 800px;
}

.task-detail-modal-large {
  max-width: 1200px;
  max-height: 90vh;
}

/* Task Status Container */
.task-status-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-icon {
  font-size: 1.2rem;
}

/* Work Timeline Styles */
.work-timeline {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.timeline-day {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.timeline-day.future {
  opacity: 0.6;
  border-left-color: #6c757d;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

.day-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.day-summary {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.work-hours {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: 500;
  font-size: 0.9rem;
}

.work-time {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.work-status {
  color: #6c757d;
  font-style: italic;
  font-size: 0.9rem;
}

/* Photo Timeline */
.photo-timeline {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.photo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.photo-placeholder {
  width: 100px;
  height: 80px;
  background: #e9ecef;
  border: 2px dashed #adb5bd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: #6c757d;
  text-align: center;
}

.photo-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.photo-time {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.photo-type {
  font-size: 0.8rem;
  color: #666;
  text-transform: capitalize;
  background: #e9ecef;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
}

.btn-complete {
  background: #28a745;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s;
  font-weight: 500;
}

.btn-complete:hover {
  background: #218838;
}

/* Fixed Close Button */
.close-button-fixed {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.close-button-fixed:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Modal Header Simple */
.modal-header-simple {
  padding: 1.5rem 3rem 1rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
}

.modal-header-simple h3 {
  margin: 0;
  color: #333;
}

/* Work Summary */
.work-summary {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  border-left: 4px solid #28a745;
}

.work-summary p {
  margin: 0;
  color: #333;
  line-height: 1.5;
}

/* Photo Documentation */
.photo-documentation {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1rem;
}

.photo-section h6 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.photo-upload-area {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s;
}

.photo-upload-area:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.photo-upload-area label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
  font-size: 0.9rem;
}

.photo-upload-area input[type="file"] {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Timeline Day Status */
.timeline-day.pending {
  opacity: 0.6;
  border-left-color: #6c757d;
}

.timeline-day.in_progress {
  border-left-color: #ffc107;
}

.timeline-day.completed {
  border-left-color: #28a745;
}

/* Edit Form */
.edit-form {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
}

.edit-form h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
}



.create-task-modal {
  max-width: 900px;
}

/* Form Styles */
.form-section {
  margin-bottom: 2rem;
}

.form-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #555;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.btn-cancel,
.btn-create {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s;
  font-weight: 500;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
}

.btn-create {
  background: #28a745;
  color: white;
}

.btn-create:hover:not(:disabled) {
  background: #218838;
}

.btn-create:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-button:hover {
  background: #f8f9fa;
  color: #333;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.task-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.detail-section {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.detail-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
}

.detail-section p {
  margin: 0.5rem 0;
  color: #666;
  line-height: 1.4;
}

.timeline-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.timeline-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

/* Mobile Responsive for New Components */
@media (max-width: 768px) {
  .task-nav {
    flex-direction: column;
  }

  .task-nav .nav-button {
    border-bottom: 1px solid #dee2e6;
    border-right: none;
  }

  .task-nav .nav-button:last-child {
    border-bottom: none;
  }

  .photo-upload-grid {
    grid-template-columns: 1fr;
  }

  .task-detail-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modal-content {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .task-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .photo-upload-section {
    padding: 0.75rem;
  }

  .upload-zone {
    padding: 0.75rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-section {
    margin-bottom: 1.5rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.6rem;
    font-size: 0.9rem;
  }

  /* Timeline Mobile Styles */
  .task-detail-modal-large {
    max-width: calc(100vw - 1rem);
    max-height: 95vh;
  }

  .day-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .day-summary {
    gap: 0.5rem;
  }

  .photo-timeline {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.75rem;
  }

  .photo-placeholder {
    width: 80px;
    height: 60px;
    font-size: 0.7rem;
  }

  .timeline-day {
    padding: 1rem;
  }

  /* Photo Documentation Mobile */
  .photo-documentation {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .photo-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.75rem;
  }

  .close-button-fixed {
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
    top: 0.75rem;
    right: 0.75rem;
  }

  .modal-header-simple {
    padding: 1rem 2.5rem 1rem 1rem;
  }

  .work-summary {
    padding: 0.75rem;
  }

  .edit-form {
    padding: 1.5rem;
  }


}

@media (max-width: 480px) {
  .login-card {
    padding: 1rem;
    margin: 0.25rem;
  }

  .dashboard-header {
    padding: 0.5rem;
    gap: 0.25rem;
  }

  .dashboard-header h1 {
    font-size: 1.1rem;
  }

  .user-info span {
    font-size: 0.75rem;
  }

  .logout-button {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .nav-button {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }

  .dashboard-section {
    padding: 0.75rem;
  }

  .current-time h3 {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .users-table {
    min-width: 500px;
  }

  .users-table th,
  .users-table td {
    padding: 0.4rem 0.2rem;
    font-size: 0.8rem;
  }

  /* Task Management Small Mobile */
  .task-header h2 {
    font-size: 1.1rem;
  }

  .admin-task-card {
    padding: 0.75rem;
  }

  .task-header-info h4 {
    font-size: 1rem;
  }

  .customer-info,
  .service-info,
  .assignment-info {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 360px) {
  .dashboard-header {
    padding: 0.4rem;
    gap: 0.2rem;
  }

  .dashboard-header h1 {
    font-size: 1rem;
  }

  .user-info {
    gap: 0.3rem;
  }

  .user-info span {
    font-size: 0.7rem;
  }

  .logout-button {
    padding: 0.3rem 0.5rem;
    font-size: 0.75rem;
  }

  .nav-button {
    padding: 0.5rem 0.6rem;
    font-size: 0.75rem;
  }
}
