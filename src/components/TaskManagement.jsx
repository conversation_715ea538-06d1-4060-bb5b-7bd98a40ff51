import { useState, useEffect } from 'react'
import { getStoredAuth } from '../utils/auth'
import CreateTaskModal from './CreateTaskModal'

const TaskManagement = () => {
  const [tasks, setTasks] = useState([])
  const [employees, setEmployees] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [filters, setFilters] = useState({
    status: '',
    date: new Date().toISOString().split('T')[0], // 默认今天
    assigned_employee_id: ''
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedTask, setSelectedTask] = useState(null)
  const [showTaskDetail, setShowTaskDetail] = useState(false)

  useEffect(() => {
    fetchTasks()
    fetchEmployees()
  }, [filters])

  const fetchTasks = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const queryParams = new URLSearchParams()
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          if (key === 'date') {
            // 将date转换为start_date和end_date
            queryParams.append('start_date', filters[key])
            queryParams.append('end_date', filters[key])
          } else {
            queryParams.append(key, filters[key])
          }
        }
      })

      const response = await fetch(`/api/tasks?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      } else {
        setError('Failed to fetch tasks')
      }
    } catch (err) {
      setError('Error fetching tasks')
    } finally {
      setLoading(false)
    }
  }

  const fetchEmployees = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setEmployees(data.filter(user => user.role === 'employee'))
      }
    } catch (err) {
      console.error('Error fetching employees:', err)
    }
  }

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getStatusColor = (status, task) => {
    const today = new Date().toISOString().split('T')[0]
    const startDate = task?.start_date

    switch (status) {
      case 'pending':
      case 'assigned':
        // 🔴 未开始/超期 - 如果开始日期已过但未开始工作
        return startDate && startDate < today ? '#dc3545' : '#ffc107'
      case 'in_progress':
        // 🟡 进行中
        return '#ffc107'
      case 'completed':
        // 🟢 已完成
        return '#28a745'
      case 'skipped':
        // ⚪ 已跳过
        return '#6c757d'
      default: return '#6c757d'
    }
  }

  const getStatusIcon = (status, task) => {
    const today = new Date().toISOString().split('T')[0]
    const startDate = task?.start_date

    switch (status) {
      case 'pending':
      case 'assigned':
        return startDate && startDate < today ? '🔴' : '🟡'
      case 'in_progress':
        return '🟡'
      case 'completed':
        return '🟢'
      case 'skipped':
        return '⚪'
      default: return '⚪'
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  const formatCurrency = (amount) => {
    if (!amount) return '£0.00'
    return `£${parseFloat(amount).toFixed(2)}`
  }

  const handleTaskClick = (task) => {
    setSelectedTask(task)
    setShowTaskDetail(true)
  }

  const handleAssignTask = async (taskId, employeeId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          assigned_employee_id: employeeId,
          status: 'assigned'
        })
      })

      if (response.ok) {
        fetchTasks()
      } else {
        setError('Failed to assign task')
      }
    } catch (err) {
      setError('Error assigning task')
    }
  }

  const handleCompleteTask = async (taskId, isEarlyCompletion = false) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const updateData = {
        status: 'completed',
        actual_end_date: new Date().toISOString().split('T')[0]
      }

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify(updateData)
      })

      if (response.ok) {
        // If early completion, mark remaining work sessions as skipped
        if (isEarlyCompletion) {
          // This would be implemented to update remaining sessions
          console.log('Early completion - marking remaining days as skipped')
        }

        fetchTasks()
        setShowTaskDetail(false)
      } else {
        setError('Failed to complete task')
      }
    } catch (err) {
      setError('Error completing task')
    }
  }

  if (loading) {
    return <div className="loading">Loading tasks...</div>
  }

  return (
    <div className="task-management">
      {error && <div className="error-message">{error}</div>}
      
      {/* Task Header */}
      <div className="task-header">
        <h2>Task Management</h2>
        <button 
          className="action-button primary"
          onClick={() => setShowCreateModal(true)}
        >
          Create New Task
        </button>
      </div>

      {/* Task Filters */}
      <div className="task-filters">
        <div className="filter-group">
          <label>Status</label>
          <select 
            className="status-filter"
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="assigned">Assigned</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Date Filter</label>
          <input
            type="date"
            className="date-filter"
            value={filters.date}
            onChange={(e) => handleFilterChange('date', e.target.value)}
            lang="en-US"
          />
        </div>

        <div className="filter-group">
          <label>Assigned Employee</label>
          <select 
            className="employee-filter"
            value={filters.assigned_employee_id}
            onChange={(e) => handleFilterChange('assigned_employee_id', e.target.value)}
          >
            <option value="">All Employees</option>
            {employees.map(employee => (
              <option key={employee.id} value={employee.id}>
                {employee.full_name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Task List */}
      <div className="admin-tasks-container">
        {tasks.length === 0 ? (
          <div className="no-tasks">
            <p>No tasks found matching the current filters.</p>
          </div>
        ) : (
          tasks.map(task => (
            <div
              key={task.id}
              className={`admin-task-card ${task.status}`}
            >
              <div className="task-header-info">
                <h4>{task.service_type} - {task.customer_name}</h4>
                <div className="task-status-container">
                  <span className="status-icon">{getStatusIcon(task.status, task)}</span>
                  <span
                    className="task-status"
                    style={{ backgroundColor: getStatusColor(task.status, task) }}
                  >
                    {task.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>

              <div className="task-details">
                <div className="customer-info">
                  <strong>Customer Details:</strong><br />
                  Name: {task.customer_name}<br />
                  Phone: {task.customer_phone}<br />
                  Postcode: {task.customer_postcode}<br />
                  {task.customer_email && `Email: ${task.customer_email}`}
                </div>

                <div className="service-info">
                  <strong>Service Details:</strong><br />
                  Type: {task.service_type}<br />
                  Description: {task.service_description}<br />
                  Fee: {formatCurrency(task.service_fee)}
                </div>

                <div className="assignment-info">
                  <strong>Schedule & Assignment:</strong><br />
                  Start: {formatDate(task.start_date)}<br />
                  Est. End: {formatDate(task.estimated_end_date)}<br />
                  {task.actual_end_date && `Actual End: ${formatDate(task.actual_end_date)}`}<br />
                  Assigned: {task.assigned_employee_name || 'Unassigned'}
                </div>
              </div>

              <div className="task-actions" onClick={(e) => e.stopPropagation()}>
                {task.status === 'pending' && (
                  <select 
                    className="btn-assign"
                    onChange={(e) => {
                      if (e.target.value) {
                        handleAssignTask(task.id, e.target.value)
                      }
                    }}
                    defaultValue=""
                  >
                    <option value="">Assign to...</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.full_name}
                      </option>
                    ))}
                  </select>
                )}
                
                {(task.status === 'in_progress' || task.status === 'assigned') && (
                  <button 
                    className="btn-view"
                    onClick={() => handleCompleteTask(task.id)}
                  >
                    Mark Complete
                  </button>
                )}
                
                <button className="btn-edit">Edit</button>
                <button
                  className="btn-view"
                  onClick={() => handleTaskClick(task)}
                >
                  View Details
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Task Detail Modal */}
      {showTaskDetail && selectedTask && (
        <TaskDetailModal 
          task={selectedTask}
          onClose={() => setShowTaskDetail(false)}
          onUpdate={fetchTasks}
        />
      )}

      {/* Create Task Modal */}
      {showCreateModal && (
        <CreateTaskModal 
          employees={employees}
          onClose={() => setShowCreateModal(false)}
          onSuccess={fetchTasks}
        />
      )}
    </div>
  )
}

// Task Detail Modal with timeline and photos
const TaskDetailModal = ({ task, onClose, onUpdate }) => {
  const [workSessions, setWorkSessions] = useState([])
  const [photos, setPhotos] = useState([])

  // Mock data for demonstration - in real app would fetch from API
  const mockWorkSessions = [
    {
      id: 1,
      work_date: task.start_date,
      start_time: '09:00',
      end_time: '12:30',
      total_hours: 3.5,
      status: 'completed',
      photos: [
        { type: 'start', url: '/mock-photo1.jpg', timestamp: '09:00' },
        { type: 'progress', url: '/mock-photo2.jpg', timestamp: '10:30' },
        { type: 'end', url: '/mock-photo3.jpg', timestamp: '12:30' }
      ]
    },
    {
      id: 2,
      work_date: new Date(new Date(task.start_date).getTime() + 24*60*60*1000).toISOString().split('T')[0],
      start_time: '08:30',
      end_time: '16:00',
      total_hours: 7.5,
      status: 'completed',
      photos: [
        { type: 'start', url: '/mock-photo4.jpg', timestamp: '08:30' },
        { type: 'progress', url: '/mock-photo5.jpg', timestamp: '12:00' },
        { type: 'end', url: '/mock-photo6.jpg', timestamp: '16:00' }
      ]
    }
  ]

  const getTotalWorkHours = () => {
    return mockWorkSessions.reduce((total, session) => total + session.total_hours, 0)
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return '🟢'
      case 'in_progress': return '🟡'
      case 'skipped': return '⚪'
      default: return '🔴'
    }
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content task-detail-modal-large" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{getStatusIcon(task.status)} {task.service_type} - {task.customer_name}</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          {/* Basic Information Grid */}
          <div className="task-detail-grid">
            <div className="detail-section">
              <h4>👤 Customer Information</h4>
              <p><strong>Name:</strong> {task.customer_name}</p>
              <p><strong>Phone:</strong> {task.customer_phone}</p>
              <p><strong>Postcode:</strong> {task.customer_postcode}</p>
              {task.customer_email && <p><strong>Email:</strong> {task.customer_email}</p>}
            </div>

            <div className="detail-section">
              <h4>🔧 Service Details</h4>
              <p><strong>Type:</strong> {task.service_type}</p>
              <p><strong>Description:</strong> {task.service_description}</p>
              <p><strong>Fee:</strong> {task.service_fee ? `£${parseFloat(task.service_fee).toFixed(2)}` : 'Not set'}</p>
            </div>

            <div className="detail-section">
              <h4>📅 Schedule & Assignment</h4>
              <p><strong>Start Date:</strong> {new Date(task.start_date).toLocaleDateString('en-GB')}</p>
              <p><strong>Est. End Date:</strong> {new Date(task.estimated_end_date).toLocaleDateString('en-GB')}</p>
              {task.actual_end_date && (
                <p><strong>Actual End:</strong> {new Date(task.actual_end_date).toLocaleDateString('en-GB')}</p>
              )}
              <p><strong>Assigned Employee:</strong> {task.assigned_employee_name || 'Unassigned'}</p>
              <p><strong>Total Work Hours:</strong> {getTotalWorkHours()} hours</p>
            </div>
          </div>

          {/* Work Timeline */}
          <div className="timeline-section">
            <h4>📊 Work Timeline & Photo Documentation</h4>
            <div className="work-timeline">
              {mockWorkSessions.map((session, index) => (
                <div key={session.id} className="timeline-day">
                  <div className="day-header">
                    <h5>
                      {getStatusIcon(session.status)} Day {index + 1} - {new Date(session.work_date).toLocaleDateString('en-GB')}
                    </h5>
                    <div className="day-summary">
                      <span className="work-hours">{session.total_hours}h</span>
                      <span className="work-time">{session.start_time} - {session.end_time}</span>
                    </div>
                  </div>

                  <div className="photo-timeline">
                    {session.photos.map((photo, photoIndex) => (
                      <div key={photoIndex} className="photo-item">
                        <div className="photo-placeholder">
                          📷 {photo.type.toUpperCase()}
                        </div>
                        <div className="photo-info">
                          <span className="photo-time">{photo.timestamp}</span>
                          <span className="photo-type">{photo.type}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}

              {/* Future days if multi-day task */}
              {new Date(task.estimated_end_date) > new Date(mockWorkSessions[mockWorkSessions.length - 1]?.work_date || task.start_date) && (
                <div className="timeline-day future">
                  <div className="day-header">
                    <h5>⚪ Day 3 - {new Date(new Date(task.start_date).getTime() + 2*24*60*60*1000).toLocaleDateString('en-GB')}</h5>
                    <div className="day-summary">
                      <span className="work-status">Skipped (Early Completion)</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn-edit" onClick={onClose}>Close</button>
          {task.status === 'in_progress' && (
            <>
              <button
                className="btn-complete"
                onClick={() => {
                  const isEarlyCompletion = new Date() < new Date(task.estimated_end_date)
                  handleCompleteTask(task.id, isEarlyCompletion)
                }}
              >
                {new Date() < new Date(task.estimated_end_date) ? 'Complete Early' : 'Mark as Complete'}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default TaskManagement
