import { useState, useEffect } from 'react'
import TaskManagement from './TaskManagement'
import { fetchWithAuth } from '../utils/auth'

const AdminDashboard = ({ user, onLogout }) => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetchWithAuth('/users')
      
      if (!response) return // Redirected to login
      
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      
      const usersData = await response.json()
      setUsers(usersData)
    } catch (error) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleDashboardClick = () => {
    setActiveTab('overview')
  }

  const renderTaskManagement = () => {
    return <TaskManagement />
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'tasks':
        return renderTaskManagement()
      case 'statistics':
        return (
          <div className="dashboard-section">
            <h2>Statistics</h2>
            <p>Statistics and reports coming soon...</p>
          </div>
        )
      case 'archive':
        return (
          <div className="dashboard-section">
            <h2>Archive</h2>
            <p>Archive functionality coming soon...</p>
          </div>
        )
      default:
        return (
          <>
            <div className="dashboard-section">
              <h2>System Overview</h2>
              <div className="stats-grid">
                <div className="stat-card">
                  <h3>Total Users</h3>
                  <p className="stat-number">{users.length}</p>
                </div>
                <div className="stat-card">
                  <h3>Employees</h3>
                  <p className="stat-number">{users.filter(u => u.role === 'employee').length}</p>
                </div>
                <div className="stat-card">
                  <h3>Admins</h3>
                  <p className="stat-number">{users.filter(u => u.role === 'admin').length}</p>
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>User Management</h2>
              {loading ? (
                <p>Loading users...</p>
              ) : error ? (
                <p className="error-message">Error: {error}</p>
              ) : (
                <div className="users-table-container">
                  <table className="users-table">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Role</th>
                        <th>Created</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map(user => (
                        <tr key={user.id}>
                          <td>{user.id}</td>
                          <td>{user.username}</td>
                          <td>{user.full_name}</td>
                          <td>
                            <span className={`role-badge ${user.role}`}>
                              {user.role}
                            </span>
                          </td>
                          <td>{new Date(user.created_at).toLocaleDateString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            <div className="dashboard-section">
              <h2>Quick Actions</h2>
              <div className="actions-grid">
                <button className="action-button">Add New Employee</button>
                <button className="action-button">View Reports</button>
                <button className="action-button">System Settings</button>
                <button className="action-button">Backup Data</button>
              </div>
            </div>
          </>
        )
    }
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1 className="dashboard-title" onClick={handleDashboardClick}>Dashboard</h1>
        <div className="user-info">
          <span>Welcome, {user.full_name}</span>
          <button onClick={onLogout} className="logout-button">Logout</button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button
          className={`nav-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`nav-button ${activeTab === 'tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('tasks')}
        >
          Tasks
        </button>
        <button
          className={`nav-button ${activeTab === 'statistics' ? 'active' : ''}`}
          onClick={() => setActiveTab('statistics')}
        >
          Statistics
        </button>
        <button
          className={`nav-button ${activeTab === 'archive' ? 'active' : ''}`}
          onClick={() => setActiveTab('archive')}
        >
          Archive
        </button>
      </nav>

      <main className="dashboard-content">
        {renderContent()}
      </main>
    </div>
  )
}

export default AdminDashboard
