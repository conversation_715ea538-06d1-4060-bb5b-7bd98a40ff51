import { useState, useEffect } from 'react'
import { getStoredAuth } from '../utils/auth'

const EmployeeTasks = ({ user }) => {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('available')
  const [selectedTask, setSelectedTask] = useState(null)
  const [showTaskDetail, setShowTaskDetail] = useState(false)

  useEffect(() => {
    fetchTasks()
  }, [])

  const fetchTasks = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch('/api/tasks', {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      } else {
        setError('Failed to fetch tasks')
      }
    } catch (err) {
      setError('Error fetching tasks')
    } finally {
      setLoading(false)
    }
  }

  const getAvailableTasks = () => {
    return tasks.filter(task => 
      task.status === 'pending' || 
      (task.status === 'assigned' && task.assigned_employee_id === user.id)
    )
  }

  const getMyTasks = () => {
    return tasks.filter(task => task.assigned_employee_id === user.id)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#ffc107'
      case 'assigned': return '#17a2b8'
      case 'in_progress': return '#007bff'
      case 'completed': return '#28a745'
      case 'cancelled': return '#dc3545'
      default: return '#6c757d'
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  const formatCurrency = (amount) => {
    if (!amount) return '£0.00'
    return `£${parseFloat(amount).toFixed(2)}`
  }

  const calculateDistance = (postcode) => {
    // Mock distance calculation - in real app would use geolocation API
    const distances = {
      'SW1A 1AA': 2.3,
      'M1 1AA': 15.7,
      'B1 1AA': 8.4,
      'L1 1AA': 12.1
    }
    return distances[postcode] || Math.floor(Math.random() * 20) + 1
  }

  const handleStartTask = async (taskId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          status: 'in_progress'
        })
      })

      if (response.ok) {
        fetchTasks()
      } else {
        setError('Failed to start task')
      }
    } catch (err) {
      setError('Error starting task')
    }
  }

  const handleCompleteTask = async (taskId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          status: 'completed',
          actual_end_date: new Date().toISOString().split('T')[0]
        })
      })

      if (response.ok) {
        fetchTasks()
      } else {
        setError('Failed to complete task')
      }
    } catch (err) {
      setError('Error completing task')
    }
  }

  const handleTaskClick = (task) => {
    setSelectedTask(task)
    setShowTaskDetail(true)
  }

  if (loading) {
    return <div className="loading">Loading tasks...</div>
  }

  const availableTasks = getAvailableTasks()
  const myTasks = getMyTasks()

  return (
    <div className="employee-tasks">
      {error && <div className="error-message">{error}</div>}
      
      {/* Task Navigation */}
      <div className="task-nav">
        <button
          className={`nav-button ${activeTab === 'available' ? 'active' : ''}`}
          onClick={() => setActiveTab('available')}
        >
          Available Tasks ({availableTasks.length})
        </button>
        <button
          className={`nav-button ${activeTab === 'my-tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('my-tasks')}
        >
          My Tasks ({myTasks.length})
        </button>
      </div>

      {/* Available Tasks Tab */}
      {activeTab === 'available' && (
        <div className="tasks-section">
          <h3>Available Tasks</h3>
          {availableTasks.length === 0 ? (
            <div className="no-tasks">
              <p>No available tasks at the moment.</p>
            </div>
          ) : (
            <div className="tasks-container">
              {availableTasks.map(task => (
                <div 
                  key={task.id} 
                  className={`task-card ${task.status}`}
                  onClick={() => handleTaskClick(task)}
                >
                  <div className="task-header-info">
                    <h4>{task.service_type} - {task.customer_name}</h4>
                    <div className="task-meta">
                      <span 
                        className="task-status"
                        style={{ backgroundColor: getStatusColor(task.status) }}
                      >
                        {task.status.replace('_', ' ').toUpperCase()}
                      </span>
                      <span className="task-distance">
                        📍 {calculateDistance(task.customer_postcode)} miles
                      </span>
                    </div>
                  </div>

                  <div className="task-content">
                    <p><strong>Description:</strong> {task.service_description}</p>
                    <p><strong>Location:</strong> {task.customer_postcode}</p>
                    <p><strong>Start Date:</strong> {formatDate(task.start_date)}</p>
                    <p><strong>Fee:</strong> {formatCurrency(task.service_fee)}</p>
                  </div>

                  <div className="task-actions" onClick={(e) => e.stopPropagation()}>
                    {task.status === 'pending' && (
                      <button 
                        className="btn-assign"
                        onClick={() => handleStartTask(task.id)}
                      >
                        Accept Task
                      </button>
                    )}
                    {task.status === 'assigned' && task.assigned_employee_id === user.id && (
                      <button 
                        className="btn-view"
                        onClick={() => handleStartTask(task.id)}
                      >
                        Start Work
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* My Tasks Tab */}
      {activeTab === 'my-tasks' && (
        <div className="tasks-section">
          <h3>My Tasks</h3>
          {myTasks.length === 0 ? (
            <div className="no-tasks">
              <p>You have no assigned tasks.</p>
            </div>
          ) : (
            <div className="tasks-container">
              {myTasks.map(task => (
                <div 
                  key={task.id} 
                  className={`task-card ${task.status}`}
                  onClick={() => handleTaskClick(task)}
                >
                  <div className="task-header-info">
                    <h4>{task.service_type} - {task.customer_name}</h4>
                    <span 
                      className="task-status"
                      style={{ backgroundColor: getStatusColor(task.status) }}
                    >
                      {task.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>

                  <div className="task-content">
                    <p><strong>Description:</strong> {task.service_description}</p>
                    <p><strong>Customer:</strong> {task.customer_name}</p>
                    <p><strong>Phone:</strong> {task.customer_phone}</p>
                    <p><strong>Location:</strong> {task.customer_postcode}</p>
                    <p><strong>Start Date:</strong> {formatDate(task.start_date)}</p>
                    <p><strong>Est. End:</strong> {formatDate(task.estimated_end_date)}</p>
                    {task.actual_end_date && (
                      <p><strong>Completed:</strong> {formatDate(task.actual_end_date)}</p>
                    )}
                  </div>

                  {/* Photo Upload Areas */}
                  {task.status === 'in_progress' && (
                    <div className="photo-upload-section">
                      <h5>Photo Verification</h5>
                      <div className="photo-upload-grid">
                        <div className="photo-upload-area">
                          <label>Start Photos</label>
                          <div className="upload-zone">
                            <p>📷 Upload start photos</p>
                            <input type="file" multiple accept="image/*" />
                          </div>
                        </div>
                        <div className="photo-upload-area">
                          <label>Progress Photos</label>
                          <div className="upload-zone">
                            <p>📷 Upload progress photos</p>
                            <input type="file" multiple accept="image/*" />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="task-actions" onClick={(e) => e.stopPropagation()}>
                    {task.status === 'assigned' && (
                      <button 
                        className="btn-view"
                        onClick={() => handleStartTask(task.id)}
                      >
                        Start Work
                      </button>
                    )}
                    {task.status === 'in_progress' && (
                      <button 
                        className="btn-assign"
                        onClick={() => handleCompleteTask(task.id)}
                      >
                        Complete Task
                      </button>
                    )}
                    <button className="btn-view">View Details</button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Task Detail Modal */}
      {showTaskDetail && selectedTask && (
        <TaskDetailModal 
          task={selectedTask}
          user={user}
          onClose={() => setShowTaskDetail(false)}
          onUpdate={fetchTasks}
        />
      )}
    </div>
  )
}

// Task Detail Modal Component
const TaskDetailModal = ({ task, user, onClose, onUpdate }) => {
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content task-detail-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{task.service_type} - {task.customer_name}</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="modal-body">
          <div className="task-detail-grid">
            <div className="detail-section">
              <h4>Customer Information</h4>
              <p><strong>Name:</strong> {task.customer_name}</p>
              <p><strong>Phone:</strong> {task.customer_phone}</p>
              <p><strong>Postcode:</strong> {task.customer_postcode}</p>
              {task.customer_email && <p><strong>Email:</strong> {task.customer_email}</p>}
            </div>
            
            <div className="detail-section">
              <h4>Service Details</h4>
              <p><strong>Type:</strong> {task.service_type}</p>
              <p><strong>Description:</strong> {task.service_description}</p>
              <p><strong>Fee:</strong> {task.service_fee ? `£${parseFloat(task.service_fee).toFixed(2)}` : 'Not set'}</p>
            </div>
            
            <div className="detail-section">
              <h4>Schedule</h4>
              <p><strong>Start Date:</strong> {new Date(task.start_date).toLocaleDateString('en-GB')}</p>
              <p><strong>Est. End Date:</strong> {new Date(task.estimated_end_date).toLocaleDateString('en-GB')}</p>
              {task.actual_end_date && (
                <p><strong>Actual End:</strong> {new Date(task.actual_end_date).toLocaleDateString('en-GB')}</p>
              )}
            </div>
          </div>
          
          <div className="timeline-section">
            <h4>Work Timeline</h4>
            <p>Timeline view coming soon...</p>
          </div>
        </div>
        
        <div className="modal-footer">
          <button className="btn-edit" onClick={onClose}>Close</button>
        </div>
      </div>
    </div>
  )
}

export default EmployeeTasks
