import React from 'react'

const TaskWorkflow = () => {
  return (
    <div className="workflow-diagram">
      <h3>📋 Multi-Day Task Workflow</h3>
      <div className="sequence-diagram">
        <div className="sequence-step">
          <div className="actor admin">👨‍💼 Admin</div>
          <div className="arrow">→</div>
          <div className="action">Create 3-day task (Start Date - End Date)</div>
          <div className="arrow">→</div>
          <div className="actor system">🖥️ System</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor system">🖥️ System</div>
          <div className="arrow">→</div>
          <div className="action">Generate 3 date blocks in DB</div>
          <div className="arrow">→</div>
          <div className="actor database">🗄️ Database</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor employee">👷‍♂️ Employee</div>
          <div className="arrow">→</div>
          <div className="action">Accept task</div>
          <div className="arrow">→</div>
          <div className="actor system">🖥️ System</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor employee">👷‍♂️ Employee</div>
          <div className="arrow">→</div>
          <div className="action">Day 1: Upload photos & work hours</div>
          <div className="arrow">→</div>
          <div className="actor system">🖥️ System</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor employee">👷‍♂️ Employee</div>
          <div className="arrow">→</div>
          <div className="action">Day 2: Upload photos & work hours</div>
          <div className="arrow">→</div>
          <div className="actor system">🖥️ System</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor employee">👷‍♂️ Employee</div>
          <div className="arrow">→</div>
          <div className="action">Click "Complete Early"</div>
          <div className="arrow">→</div>
          <div className="actor system">🖥️ System</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor system">🖥️ System</div>
          <div className="arrow">→</div>
          <div className="action">Verify first 2 days completion</div>
          <div className="arrow">→</div>
          <div className="actor system">🖥️ System</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor system">🖥️ System</div>
          <div className="arrow">→</div>
          <div className="action">Mark task complete, skip Day 3</div>
          <div className="arrow">→</div>
          <div className="actor database">🗄️ Database</div>
        </div>
        
        <div className="sequence-step">
          <div className="actor system">🖥️ System</div>
          <div className="arrow">→</div>
          <div className="action">Send notification</div>
          <div className="arrow">→</div>
          <div className="actor admin">👨‍💼 Admin</div>
        </div>
      </div>
      
      <div className="status-legend">
        <h4>Status Color Coding:</h4>
        <div className="legend-items">
          <div className="legend-item">
            <span className="status-dot red">🔴</span>
            <span>Not Started / Overdue</span>
          </div>
          <div className="legend-item">
            <span className="status-dot yellow">🟡</span>
            <span>In Progress</span>
          </div>
          <div className="legend-item">
            <span className="status-dot green">🟢</span>
            <span>Completed</span>
          </div>
          <div className="legend-item">
            <span className="status-dot gray">⚪</span>
            <span>Skipped</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TaskWorkflow
