import sqlite3 from 'sqlite3';
import bcrypt from 'bcryptjs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dbPath = path.join(__dirname, 'handyman.db');
const db = new (sqlite3.verbose().Database)(dbPath);

// Initialize database
const initDatabase = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Create users table
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          role TEXT NOT NULL CHECK(role IN ('admin', 'employee')),
          full_name TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating users table:', err);
          reject(err);
          return;
        }

        // Create tasks table
        db.run(`
          CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            customer_phone TEXT NOT NULL,
            customer_postcode TEXT NOT NULL,
            customer_email TEXT,
            service_type TEXT NOT NULL,
            service_description TEXT NOT NULL,
            start_date DATE NOT NULL,
            estimated_end_date DATE NOT NULL,
            actual_end_date DATE,
            service_fee DECIMAL(10,2),
            status TEXT NOT NULL DEFAULT 'pending' CHECK(status IN ('pending', 'assigned', 'in_progress', 'completed', 'cancelled')),
            assigned_employee_id INTEGER,
            created_by INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (assigned_employee_id) REFERENCES users(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
          )
        `, (err) => {
          if (err) {
            console.error('Error creating tasks table:', err);
            reject(err);
            return;
          }

          // Create task_photos table
          db.run(`
            CREATE TABLE IF NOT EXISTS task_photos (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              task_id INTEGER NOT NULL,
              photo_type TEXT NOT NULL CHECK(photo_type IN ('start', 'end', 'progress')),
              file_path TEXT NOT NULL,
              file_name TEXT NOT NULL,
              upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
              exif_data TEXT,
              gps_latitude REAL,
              gps_longitude REAL,
              uploaded_by INTEGER NOT NULL,
              FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
              FOREIGN KEY (uploaded_by) REFERENCES users(id)
            )
          `, (err) => {
            if (err) {
              console.error('Error creating task_photos table:', err);
              reject(err);
              return;
            }

            // Create task_work_sessions table for time tracking
            db.run(`
              CREATE TABLE IF NOT EXISTS task_work_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER NOT NULL,
                employee_id INTEGER NOT NULL,
                start_time DATETIME NOT NULL,
                end_time DATETIME,
                work_date DATE NOT NULL,
                total_hours DECIMAL(4,2),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
                FOREIGN KEY (employee_id) REFERENCES users(id)
              )
            `, (err) => {
              if (err) {
                console.error('Error creating task_work_sessions table:', err);
                reject(err);
                return;
              }

              // Check if users already exist
              db.get("SELECT COUNT(*) as count FROM users", (err, row) => {
                if (err) {
                  reject(err);
                  return;
                }

                if (row.count === 0) {
                  insertDefaultUsers().then(() => {
                    insertDefaultTasks().then(resolve).catch(reject);
                  }).catch(reject);
                } else {
                  console.log('Users already exist in database');
                  // Check if tasks exist, if not create default tasks
                  db.get("SELECT COUNT(*) as count FROM tasks", (err, row) => {
                    if (err) {
                      reject(err);
                      return;
                    }

                    if (row.count === 0) {
                      insertDefaultTasks().then(resolve).catch(reject);
                    } else {
                      console.log('Tasks already exist in database');
                      resolve();
                    }
                  });
                }
              });
            });
          });
        });
      });
    });
  });
};

// Insert default users
const insertDefaultUsers = async () => {
  const defaultPassword = 'password123';
  const hashedPassword = await bcrypt.hash(defaultPassword, 10);
  
  const users = [
    { username: 'admin', password: hashedPassword, role: 'admin', full_name: 'Administrator' },
    { username: 'tom', password: hashedPassword, role: 'employee', full_name: 'Tom Smith' },
    { username: 'john', password: hashedPassword, role: 'employee', full_name: 'John Johnson' },
    { username: 'mike', password: hashedPassword, role: 'employee', full_name: 'Mike Wilson' },
    { username: 'david', password: hashedPassword, role: 'employee', full_name: 'David Brown' },
    { username: 'james', password: hashedPassword, role: 'employee', full_name: 'James Davis' },
    { username: 'robert', password: hashedPassword, role: 'employee', full_name: 'Robert Miller' },
    { username: 'william', password: hashedPassword, role: 'employee', full_name: 'William Taylor' },
    { username: 'richard', password: hashedPassword, role: 'employee', full_name: 'Richard Anderson' },
    { username: 'charles', password: hashedPassword, role: 'employee', full_name: 'Charles Thomas' },
    { username: 'daniel', password: hashedPassword, role: 'employee', full_name: 'Daniel Jackson' }
  ];
  
  return new Promise((resolve, reject) => {
    const stmt = db.prepare("INSERT INTO users (username, password, role, full_name) VALUES (?, ?, ?, ?)");
    
    let completed = 0;
    users.forEach((user) => {
      stmt.run([user.username, user.password, user.role, user.full_name], (err) => {
        if (err) {
          console.error(`Error inserting user ${user.username}:`, err);
          reject(err);
          return;
        }
        completed++;
        if (completed === users.length) {
          stmt.finalize();
          console.log('Default users inserted successfully');
          resolve();
        }
      });
    });
  });
};

// Insert default tasks
const insertDefaultTasks = async () => {
  const tasks = [
    {
      customer_name: 'Sarah Johnson',
      customer_phone: '+44 7700 900123',
      customer_postcode: 'SW1A 1AA',
      customer_email: '<EMAIL>',
      service_type: 'Plumbing',
      service_description: 'Fix leaking kitchen tap and replace bathroom faucet',
      start_date: '2024-06-15',
      estimated_end_date: '2024-06-15',
      service_fee: 150.00,
      status: 'assigned',
      assigned_employee_id: 2, // Tom
      created_by: 1 // Admin
    },
    {
      customer_name: 'Michael Brown',
      customer_phone: '+44 7700 900456',
      customer_postcode: 'M1 1AA',
      customer_email: '<EMAIL>',
      service_type: 'Electrical',
      service_description: 'Install new light fixtures in living room and bedroom',
      start_date: '2024-06-16',
      estimated_end_date: '2024-06-17',
      service_fee: 280.00,
      status: 'in_progress',
      assigned_employee_id: 3, // John
      created_by: 1 // Admin
    },
    {
      customer_name: 'Emma Wilson',
      customer_phone: '+44 7700 900789',
      customer_postcode: 'B1 1AA',
      customer_email: '<EMAIL>',
      service_type: 'Carpentry',
      service_description: 'Build custom kitchen cabinets and install shelving',
      start_date: '2024-06-18',
      estimated_end_date: '2024-06-20',
      service_fee: 450.00,
      status: 'pending',
      assigned_employee_id: null,
      created_by: 1 // Admin
    },
    {
      customer_name: 'Robert Davis',
      customer_phone: '+44 7700 900012',
      customer_postcode: 'L1 1AA',
      customer_email: '<EMAIL>',
      service_type: 'General Maintenance',
      service_description: 'Garden fence repair and gate installation',
      start_date: '2024-06-14',
      estimated_end_date: '2024-06-14',
      actual_end_date: '2024-06-14',
      service_fee: 200.00,
      status: 'completed',
      assigned_employee_id: 4, // Mike
      created_by: 1 // Admin
    }
  ];

  return new Promise((resolve, reject) => {
    const stmt = db.prepare(`
      INSERT INTO tasks (
        customer_name, customer_phone, customer_postcode, customer_email,
        service_type, service_description, start_date, estimated_end_date,
        actual_end_date, service_fee, status, assigned_employee_id, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    let completed = 0;
    tasks.forEach(task => {
      stmt.run([
        task.customer_name, task.customer_phone, task.customer_postcode, task.customer_email,
        task.service_type, task.service_description, task.start_date, task.estimated_end_date,
        task.actual_end_date, task.service_fee, task.status, task.assigned_employee_id, task.created_by
      ], (err) => {
        if (err) {
          console.error('Error inserting task:', err);
          reject(err);
          return;
        }

        completed++;
        if (completed === tasks.length) {
          stmt.finalize();
          console.log('Default tasks created successfully');
          resolve();
        }
      });
    });
  });
};

// User authentication
const authenticateUser = (username, password) => {
  return new Promise((resolve, reject) => {
    db.get(
      "SELECT * FROM users WHERE username = ?",
      [username],
      async (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (!row) {
          resolve(null);
          return;
        }
        
        const isValid = await bcrypt.compare(password, row.password);
        if (isValid) {
          resolve({
            id: row.id,
            username: row.username,
            role: row.role,
            full_name: row.full_name
          });
        } else {
          resolve(null);
        }
      }
    );
  });
};

// Get all users (admin only)
const getAllUsers = () => {
  return new Promise((resolve, reject) => {
    db.all(
      "SELECT id, username, role, full_name, created_at FROM users ORDER BY role, username",
      (err, rows) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(rows);
      }
    );
  });
};

// Task management functions
const getAllTasks = (filters = {}) => {
  return new Promise((resolve, reject) => {
    let query = `
      SELECT t.*, u.full_name as assigned_employee_name, c.full_name as created_by_name
      FROM tasks t
      LEFT JOIN users u ON t.assigned_employee_id = u.id
      LEFT JOIN users c ON t.created_by = c.id
      WHERE 1=1
    `;
    const params = [];

    if (filters.status) {
      query += ' AND t.status = ?';
      params.push(filters.status);
    }

    if (filters.assigned_employee_id) {
      query += ' AND t.assigned_employee_id = ?';
      params.push(filters.assigned_employee_id);
    }

    if (filters.start_date) {
      query += ' AND t.start_date >= ?';
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      query += ' AND t.start_date <= ?';
      params.push(filters.end_date);
    }

    query += ' ORDER BY t.start_date DESC, t.created_at DESC';

    db.all(query, params, (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
};

const getTaskById = (id) => {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT t.*, u.full_name as assigned_employee_name, c.full_name as created_by_name
      FROM tasks t
      LEFT JOIN users u ON t.assigned_employee_id = u.id
      LEFT JOIN users c ON t.created_by = c.id
      WHERE t.id = ?
    `;

    db.get(query, [id], (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(row);
    });
  });
};

const createTask = (taskData) => {
  return new Promise((resolve, reject) => {
    const query = `
      INSERT INTO tasks (
        customer_name, customer_phone, customer_postcode, customer_email,
        service_type, service_description, start_date, estimated_end_date,
        service_fee, status, assigned_employee_id, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(query, [
      taskData.customer_name,
      taskData.customer_phone,
      taskData.customer_postcode,
      taskData.customer_email,
      taskData.service_type,
      taskData.service_description,
      taskData.start_date,
      taskData.estimated_end_date,
      taskData.service_fee,
      taskData.status || 'pending',
      taskData.assigned_employee_id,
      taskData.created_by
    ], function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ id: this.lastID });
    });
  });
};

const updateTask = (id, taskData) => {
  return new Promise((resolve, reject) => {
    const fields = [];
    const params = [];

    Object.keys(taskData).forEach(key => {
      if (taskData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(taskData[key]);
      }
    });

    if (fields.length === 0) {
      resolve({ changes: 0 });
      return;
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE tasks SET ${fields.join(', ')} WHERE id = ?`;

    db.run(query, params, function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ changes: this.changes });
    });
  });
};

const deleteTask = (id) => {
  return new Promise((resolve, reject) => {
    db.run('DELETE FROM tasks WHERE id = ?', [id], function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ changes: this.changes });
    });
  });
};

// Generate work sessions for multi-day tasks
const generateWorkSessions = (taskId, startDate, endDate) => {
  return new Promise((resolve, reject) => {
    const sessions = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Generate sessions for each day
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      sessions.push({
        task_id: taskId,
        work_date: d.toISOString().split('T')[0]
      });
    }

    if (sessions.length === 0) {
      resolve([]);
      return;
    }

    const stmt = db.prepare(`
      INSERT INTO task_work_sessions (task_id, work_date)
      VALUES (?, ?)
    `);

    let completed = 0;
    sessions.forEach(session => {
      stmt.run([session.task_id, session.work_date], (err) => {
        if (err) {
          console.error('Error inserting work session:', err);
          reject(err);
          return;
        }

        completed++;
        if (completed === sessions.length) {
          stmt.finalize();
          resolve(sessions);
        }
      });
    });
  });
};

// Get work sessions for a task
const getTaskWorkSessions = (taskId) => {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT * FROM task_work_sessions
      WHERE task_id = ?
      ORDER BY work_date ASC
    `;

    db.all(query, [taskId], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
};

// Update work session
const updateWorkSession = (sessionId, sessionData) => {
  return new Promise((resolve, reject) => {
    const fields = [];
    const params = [];

    Object.keys(sessionData).forEach(key => {
      if (sessionData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(sessionData[key]);
      }
    });

    if (fields.length === 0) {
      resolve({ changes: 0 });
      return;
    }

    params.push(sessionId);

    const query = `UPDATE task_work_sessions SET ${fields.join(', ')} WHERE id = ?`;

    db.run(query, params, function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ changes: this.changes });
    });
  });
};

export {
  db,
  initDatabase,
  authenticateUser,
  getAllUsers,
  getAllTasks,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  generateWorkSessions,
  getTaskWorkSessions,
  updateWorkSession
};
